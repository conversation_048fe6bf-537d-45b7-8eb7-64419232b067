class DragManager {
    constructor(container) {
        this.container = container;
        this.isDragging = false;
        this.dragStart = { x: 0, y: 0 };
        this.elementPosition = { x: 0, y: 0 };
        this.lastPosition = { x: 0, y: 0 };
        
        this.setupEventListeners();
    }

    setupEventListeners() {
        this.container.addEventListener('mousedown', this.startDragging.bind(this));
        document.addEventListener('mousemove', this.drag.bind(this));
        document.addEventListener('mouseup', this.stopDragging.bind(this));

        // Touch events support
        this.container.addEventListener('touchstart', this.startDragging.bind(this));
        document.addEventListener('touchmove', this.drag.bind(this));
        document.addEventListener('touchend', this.stopDragging.bind(this));
    }

    startDragging(e) {
        this.isDragging = true;
        
        // Get initial position (works for both mouse and touch events)
        const position = this.getEventPosition(e);
        this.dragStart.x = position.x;
        this.dragStart.y = position.y;
        
        // Store the last known position
        this.elementPosition.x = this.lastPosition.x || 0;
        this.elementPosition.y = this.lastPosition.y || 0;
        
        this.container.style.cursor = 'grabbing';
    }

    drag(e) {
        if (!this.isDragging) return;
        
        e.preventDefault();
        const position = this.getEventPosition(e);
        
        const deltaX = position.x - this.dragStart.x;
        const deltaY = position.y - this.dragStart.y;
        
        const newX = this.elementPosition.x + deltaX;
        const newY = this.elementPosition.y + deltaY;
        
        this.container.style.transform = `translate(${newX}px, ${newY}px)`;
        
        // Update last position for next drag
        this.lastPosition.x = newX;
        this.lastPosition.y = newY;
    }

    stopDragging() {
        this.isDragging = false;
        this.container.style.cursor = 'grab';
    }

    getEventPosition(e) {
        if (e.touches) {
            return {
                x: e.touches[0].clientX,
                y: e.touches[0].clientY
            };
        }
        return {
            x: e.clientX,
            y: e.clientY
        };
    }

    reset() {
        this.lastPosition = { x: 0, y: 0 };
        this.container.style.transform = 'translate(0px, 0px)';
    }
}

// Export the DragManager class
export default DragManager;