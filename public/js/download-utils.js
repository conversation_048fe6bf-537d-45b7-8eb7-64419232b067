/**
 * Download utilities for Mermaid charts
 * Provides functionality to download charts as PNG or SVG images
 */

/**
 * Downloads a Mermaid chart as an image
 * @param {string} format - 'png' or 'svg'
 * @param {string} containerId - ID of the container holding the Mermaid diagram
 */
function downloadChartAsImage(format, containerId) {
  try {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error('Container not found:', containerId);
      showDownloadError('Chart container not found');
      return;
    }

    // Find the SVG element within the container
    const svgElement = container.querySelector('svg');
    if (!svgElement) {
      console.error('No SVG found in container:', containerId);
      showDownloadError('No chart found to download');
      return;
    }

    // Get the chart title for filename
    const chartTitle = getChartTitle();
    const filename = sanitizeFilename(chartTitle);

    if (format === 'svg') {
      downloadAsSVG(svgElement, filename);
    } else if (format === 'png') {
      downloadAsPNG(svgElement, filename);
    } else {
      console.error('Unsupported format:', format);
      showDownloadError('Unsupported format: ' + format);
    }
  } catch (error) {
    console.error('Error downloading chart:', error);
    showDownloadError('Failed to download chart: ' + error.message);
  }
}

/**
 * Downloads an SVG element as an SVG file
 * @param {SVGElement} svgElement - The SVG element to download
 * @param {string} filename - The filename (without extension)
 */
function downloadAsSVG(svgElement, filename) {
  try {
    // Clone the SVG to avoid modifying the original
    const svgClone = svgElement.cloneNode(true);

    // Ensure the SVG has proper namespace and styling
    svgClone.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
    svgClone.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');

    // Get computed styles and embed them
    embedStyles(svgClone);

    // Convert to string
    const svgString = new XMLSerializer().serializeToString(svgClone);

    // Create blob and download
    const blob = new Blob([svgString], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = filename + '.svg';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up
    URL.revokeObjectURL(url);

    showDownloadSuccess('SVG downloaded successfully');
  } catch (error) {
    console.error('Error downloading SVG:', error);
    showDownloadError('Failed to download SVG: ' + error.message);
  }
}

/**
 * Downloads an SVG element as a PNG file
 * @param {SVGElement} svgElement - The SVG element to download
 * @param {string} filename - The filename (without extension)
 */
function downloadAsPNG(svgElement, filename) {
  try {
    // Clone the SVG to avoid modifying the original
    const svgClone = svgElement.cloneNode(true);

    // Ensure the SVG has proper namespace and styling
    svgClone.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
    svgClone.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');

    // Get computed styles and embed them
    embedStyles(svgClone);

    // Get SVG dimensions
    const svgRect = svgElement.getBoundingClientRect();
    const svgWidth = svgRect.width || svgElement.width.baseVal.value || 800;
    const svgHeight = svgRect.height || svgElement.height.baseVal.value || 600;

    // Set explicit dimensions on the clone
    svgClone.setAttribute('width', svgWidth);
    svgClone.setAttribute('height', svgHeight);

    // Convert to string
    const svgString = new XMLSerializer().serializeToString(svgClone);

    // Create canvas
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    // Set canvas size with scale factor for better quality
    const scaleFactor = 2;
    canvas.width = svgWidth * scaleFactor;
    canvas.height = svgHeight * scaleFactor;

    // Scale the context
    ctx.scale(scaleFactor, scaleFactor);

    // Clear the canvas with transparency
    ctx.clearRect(0, 0, svgWidth, svgHeight);

    // Create image from SVG
    const img = new Image();
    img.onload = function() {
      try {
        // Draw the image on canvas
        ctx.drawImage(img, 0, 0, svgWidth, svgHeight);

        // Convert canvas to blob and download
        canvas.toBlob(function(blob) {
          if (blob) {
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = filename + '.png';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up
            URL.revokeObjectURL(url);

            showDownloadSuccess('PNG downloaded successfully');
          } else {
            showDownloadError('Failed to create PNG blob');
          }
        }, 'image/png', 0.95);
      } catch (error) {
        console.error('Error converting to PNG:', error);
        showDownloadError('Failed to convert to PNG: ' + error.message);
      }
    };

    img.onerror = function(error) {
      console.error('Error loading SVG image:', error);
      // Try alternative method using html2canvas-like approach
      tryAlternativePNGMethod(svgElement, filename, svgWidth, svgHeight);
    };

    // Use data URL instead of blob URL to avoid CORS issues
    const svgDataUrl = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgString)));
    img.src = svgDataUrl;

  } catch (error) {
    console.error('Error downloading PNG:', error);
    showDownloadError('Failed to download PNG: ' + error.message);
  }
}

/**
 * Alternative PNG export method for when standard method fails
 * @param {SVGElement} svgElement - The SVG element to download
 * @param {string} filename - The filename (without extension)
 * @param {number} width - SVG width
 * @param {number} height - SVG height
 */
function tryAlternativePNGMethod(svgElement, filename, width, height) {
  try {
    console.log('Trying alternative PNG export method...');

    // Method 1: Try using the SVG directly without data URL
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const scaleFactor = 2;

    canvas.width = width * scaleFactor;
    canvas.height = height * scaleFactor;
    ctx.scale(scaleFactor, scaleFactor);

    // Clear the canvas with transparency
    ctx.clearRect(0, 0, width, height);

    // Try to render the SVG content directly to canvas using drawSvg approach
    const svgClone = svgElement.cloneNode(true);
    embedStyles(svgClone);

    // Clean up the SVG string to avoid encoding issues
    let svgString = new XMLSerializer().serializeToString(svgClone);

    // Ensure proper encoding
    svgString = svgString.replace(/[\u00A0-\u9999<>\&]/gim, function(i) {
      return '&#' + i.charCodeAt(0) + ';';
    });

    // Create a simple data URL without base64 encoding
    const svgDataUrl = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svgString);

    const img = new Image();

    img.onload = function() {
      try {
        ctx.drawImage(img, 0, 0, width, height);

        canvas.toBlob(function(blob) {
          if (blob) {
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename + '.png';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            showDownloadSuccess('PNG downloaded successfully (alternative method)');
          } else {
            // Final fallback: suggest SVG download
            showDownloadError('PNG export not supported in this browser - please use SVG format');
          }
        }, 'image/png', 0.95);
      } catch (error) {
        console.error('Alternative PNG method canvas error:', error);
        showDownloadError('PNG export failed - please use SVG format instead');
      }
    };

    img.onerror = function() {
      console.error('Alternative PNG method failed');
      showDownloadError('PNG export not supported - please use SVG format instead');
    };

    img.src = svgDataUrl;

  } catch (error) {
    console.error('Alternative PNG method error:', error);
    showDownloadError('PNG export failed - please use SVG format instead');
  }
}

/**
 * Embeds computed styles into an SVG element
 * @param {SVGElement} svgElement - The SVG element to process
 */
function embedStyles(svgElement) {
  try {
    // Get all elements in the SVG
    const elements = svgElement.querySelectorAll('*');

    // Process each element
    elements.forEach(element => {
      const computedStyle = window.getComputedStyle(element);

      // Important style properties to preserve
      const importantStyles = [
        'fill', 'stroke', 'stroke-width', 'stroke-dasharray', 'stroke-linecap',
        'font-family', 'font-size', 'font-weight', 'font-style',
        'text-anchor', 'dominant-baseline', 'alignment-baseline',
        'opacity', 'visibility', 'display'
      ];

      let styleString = '';
      importantStyles.forEach(prop => {
        const value = computedStyle.getPropertyValue(prop);
        if (value && value !== 'initial' && value !== 'normal') {
          styleString += `${prop}: ${value}; `;
        }
      });

      if (styleString) {
        element.setAttribute('style', styleString);
      }
    });
  } catch (error) {
    console.warn('Could not embed styles:', error);
  }
}

/**
 * Gets the current chart title for use in filename
 * @returns {string} The chart title or a default name
 */
function getChartTitle() {
  // Try to get title from view modal
  const viewTitle = document.getElementById('view-chart-title');
  if (viewTitle && viewTitle.textContent && viewTitle.textContent !== 'View Diagram') {
    return viewTitle.textContent;
  }

  // Try to get title from editor
  const editorTitle = document.getElementById('chart-title');
  if (editorTitle && editorTitle.value) {
    return editorTitle.value;
  }

  // Default name
  return 'mermaid-diagram';
}

/**
 * Sanitizes a filename by removing invalid characters
 * @param {string} filename - The filename to sanitize
 * @returns {string} The sanitized filename
 */
function sanitizeFilename(filename) {
  // Remove invalid filename characters and limit length
  return filename
    .replace(/[<>:"/\\|?*]/g, '-')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
    .substring(0, 100) || 'mermaid-diagram';
}

/**
 * Shows a success message for downloads
 * @param {string} message - The success message
 */
function showDownloadSuccess(message) {
  // Try to use existing notification system or fallback to console
  if (typeof showNotification === 'function') {
    showNotification(message, 'success');
  } else {
    console.log('✅ ' + message);
    // Simple visual feedback
    showTemporaryMessage(message, 'success');
  }
}

/**
 * Shows an error message for downloads
 * @param {string} message - The error message
 */
function showDownloadError(message) {
  // Try to use existing notification system or fallback to console
  if (typeof showNotification === 'function') {
    showNotification(message, 'error');
  } else {
    console.error('❌ ' + message);
    // Simple visual feedback
    showTemporaryMessage(message, 'error');
  }
}

/**
 * Shows a temporary message overlay
 * @param {string} message - The message to show
 * @param {string} type - 'success' or 'error'
 */
function showTemporaryMessage(message, type) {
  // Create message element
  const messageEl = document.createElement('div');
  messageEl.className = `download-message download-message-${type}`;
  messageEl.textContent = message;

  // Style the message
  Object.assign(messageEl.style, {
    position: 'fixed',
    top: '20px',
    right: '20px',
    padding: '12px 20px',
    borderRadius: '8px',
    color: 'white',
    fontWeight: 'bold',
    zIndex: '10000',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
    transition: 'all 0.3s ease',
    backgroundColor: type === 'success' ? '#00d4aa' : '#ff6b6b'
  });

  // Add to page
  document.body.appendChild(messageEl);

  // Animate in
  setTimeout(() => {
    messageEl.style.transform = 'translateX(0)';
    messageEl.style.opacity = '1';
  }, 10);

  // Remove after delay
  setTimeout(() => {
    messageEl.style.transform = 'translateX(100%)';
    messageEl.style.opacity = '0';
    setTimeout(() => {
      if (messageEl.parentNode) {
        messageEl.parentNode.removeChild(messageEl);
      }
    }, 300);
  }, 3000);
}

// Export functions for use in other scripts
if (typeof window !== 'undefined') {
  window.downloadChartAsImage = downloadChartAsImage;
  window.downloadAsSVG = downloadAsSVG;
  window.downloadAsPNG = downloadAsPNG;
}
