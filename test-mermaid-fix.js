// Test script to verify the Mermaid Unicode fix
// This can be run in the browser console on the dashboard page

function testMermaidUnicodeFix() {
    console.log('🧪 Testing Mermaid Unicode Character Fix...');

    // Test cases with problematic Unicode characters
    const testCases = [
        {
            name: 'Plus/Minus <PERSON>ojis (Original Error Case)',
            content: `graph TD
    A[Start] --> B{Decision}
    B -->|Yes| f1[➕]
    B -->|No| f2[➖]
    f1 --- f2`
        },
        {
            name: 'Various Emojis',
            content: `graph LR
    A[🔥 Fire] --> B[💡 Idea]
    B --> C[📊 Chart]
    C --> D[⭐ Star]`
        },
        {
            name: 'Mixed Unicode',
            content: `flowchart TD
    A[✓ Valid] --> B[❌ Invalid]
    B --> C[⚠ Warning]
    C --> D[🎯 Target]`
        },
        {
            name: 'Invisible Characters Test',
            content: `graph TD\u2000\u2001\u2002
    A[Test] --> B[Node]\u00A0
    B --> C[End]`
        },
        {
            name: 'Valid Syntax Preservation',
            content: `graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Process]
    B -->|No| D[End]
    C --> D`
        }
    ];

    let passedTests = 0;
    let totalTests = testCases.length;

    testCases.forEach((testCase, index) => {
        console.log(`\n📋 Test ${index + 1}: ${testCase.name}`);
        console.log('Original content:', testCase.content);

        try {
            // Test original content (may fail for Unicode cases)
            let originalPassed = false;
            try {
                mermaid.parse(testCase.content);
                console.log('✓ Original content passed (no Unicode issues)');
                originalPassed = true;
            } catch (originalError) {
                console.log('❌ Original content failed:', originalError.message);
                if (originalError.message.includes('UnknownDiagramError')) {
                    console.log('  → This is the exact error we\'re trying to fix!');
                }
            }

            // Test sanitized content (should always pass)
            const sanitizationResult = sanitizeMermaidContent(testCase.content);
            console.log('Sanitized content:', sanitizationResult.content);
            console.log('Has replacements:', sanitizationResult.hasReplacements);

            mermaid.parse(sanitizationResult.content);
            console.log('✅ Sanitized content passed!');

            // Additional validation
            if (sanitizationResult.hasReplacements && originalPassed) {
                console.log('  ⚠️ Note: Replacements made even though original passed');
            } else if (!sanitizationResult.hasReplacements && !originalPassed) {
                console.log('  ⚠️ Note: No replacements made but original failed - may need investigation');
            }

            passedTests++;

        } catch (error) {
            console.log('❌ Test failed:', error.message);
        }
    });

    console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);

    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! The Unicode fix is working correctly.');
        console.log('\n🔍 Key improvements:');
        console.log('  • No more "UnknownDiagramError" from Unicode characters');
        console.log('  • Conservative replacement strategy preserves valid syntax');
        console.log('  • Known Unicode characters mapped to meaningful alternatives');
        console.log('  • Unknown emojis replaced with "emoji" instead of breaking characters');
    } else {
        console.log('⚠️ Some tests failed. The fix may need adjustment.');
        console.log('\nPlease check the individual test results above for details.');
    }

    return {
        passed: passedTests,
        total: totalTests,
        success: passedTests === totalTests
    };
}

// Function to test the sanitization function specifically
function testSanitizationFunction() {
    console.log('\n🔧 Testing sanitization function...');

    const testInputs = [
        { input: 'f1[➕]', expected: '+', description: 'Plus emoji to plus sign' },
        { input: 'f2[➖]', expected: '-', description: 'Minus emoji to minus sign' },
        { input: 'A[🔥 Fire]', expected: 'fire', description: 'Fire emoji to "fire"' },
        { input: 'B[✓ Valid]', expected: 'check', description: 'Checkmark to "check"' },
        { input: 'C[❌ Invalid]', expected: 'x', description: 'X mark to "x"' },
        { input: 'D[\u2000\u2001Test]', expected: ' ', description: 'Invisible chars to space' },
        { input: 'E[😀]', expected: 'emoji', description: 'Unknown emoji to "emoji"' }
    ];

    testInputs.forEach((test, index) => {
        const result = sanitizeMermaidContent(test.input);
        const passed = result.content.includes(test.expected);
        console.log(`Test ${index + 1}: ${test.description}`);
        console.log(`  Input:  ${test.input}`);
        console.log(`  Output: ${result.content}`);
        console.log(`  Expected: Contains "${test.expected}"`);
        console.log(`  Result: ${passed ? '✅ PASS' : '❌ FAIL'}\n`);
    });
}

// Export for use in browser console
if (typeof window !== 'undefined') {
    window.testMermaidUnicodeFix = testMermaidUnicodeFix;
    window.testSanitizationFunction = testSanitizationFunction;
}

console.log('🚀 Test functions loaded.');
console.log('Run testMermaidUnicodeFix() to test the complete fix.');
console.log('Run testSanitizationFunction() to test individual character replacements.');
