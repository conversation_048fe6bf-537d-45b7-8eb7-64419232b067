<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Shared Diagram - mermantic</title>
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/nui/nui.css">
</head>
<body>
  <!-- Global Navigation Container -->
  <div id="navigation-container"></div>

  <main class="container">
    <section id="shared-chart" class="shared-chart">
      <div id="loading-message">Loading diagram...</div>

      <div id="chart-container" style="display: none;">
        <h2 id="chart-title"></h2>
        <div id="chart-content" class="mermaid-container"></div>
        <div class="chart-actions">
          <a id="view-standalone" class="nui-button small" target="_blank">View Standalone</a>
        </div>
      </div>

      <div id="error-message" class="error-message" style="display: none;"></div>
    </section>
  </main>

  <!-- Global Footer Container -->
  <div id="footer-container"></div>

  <!-- Component Scripts -->
  <script src="/js/components/navigation.js"></script>
  <script src="/js/components/footer.js"></script>
  <script src="/js/components/analytics-manager.js"></script>
  <script src="/js/components/seo-manager.js"></script>
  <script src="/js/components/component-loader.js"></script>

  <script src="/js/auth.js"></script>
  <script src="/js/zoom-controls.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Check auth status
      checkAuthStatus();

      // Get share ID from URL
      const urlParams = new URLSearchParams(window.location.search);
      const shareId = urlParams.get('id');

      if (!shareId) {
        document.getElementById('loading-message').style.display = 'none';
        document.getElementById('error-message').textContent = 'No diagram ID provided';
        document.getElementById('error-message').style.display = 'block';
        return;
      }

      loadSharedChart(shareId);
    });

    async function loadSharedChart(shareId) {
      try {
        const response = await fetch(`/api/charts/share/${shareId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to load diagram');
        }

        const data = await response.json();
        const chart = data.chart;

        document.getElementById('chart-title').textContent = chart.title;

        // Update SEO for this specific diagram
        if (window.seo) {
          const diagramSEO = SEOManager.getSharedDiagramSEO(chart);
          window.seo.init(diagramSEO);
        }

        // Create a mermaid div for the content
        const chartContent = document.getElementById('chart-content');
        chartContent.innerHTML = '';

        // Remove any existing zoom controls
        const chartContentParent = chartContent.parentNode;
        if (chartContentParent) {
          const existingControls = chartContentParent.querySelectorAll('.zoom-controls');
          existingControls.forEach(control => control.remove());
        }

        try {
          // Use server-side rendering with theme support
          const renderResponse = await fetch('/api/render/html', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              content: chart.content,
              title: chart.title,
              theme: chart.theme || 'dark'
            })
          });

          if (!renderResponse.ok) {
            throw new Error('Failed to render diagram');
          }

          const renderData = await renderResponse.json();

          // Create an iframe to display the rendered HTML
          const iframe = document.createElement('iframe');
          iframe.id = 'chart-iframe';
          iframe.style.width = '100%';
          iframe.style.height = '500px';
          iframe.style.border = 'none';
          iframe.style.overflow = 'hidden';

          // Append the iframe to the content div
          chartContent.appendChild(iframe);

          // Set the iframe content
          const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
          iframeDoc.open();
          iframeDoc.write(renderData.html);
          iframeDoc.close();

          // Add zoom controls directly
          const zoomControls = document.createElement('div');
          zoomControls.className = 'zoom-controls';
          zoomControls.innerHTML = `
            <button class="zoom-out" title="Zoom Out">-</button>
            <button class="zoom-reset" title="Reset Zoom">↺</button>
            <button class="zoom-in" title="Zoom In">+</button>
          `;

          // Insert zoom controls before the iframe container
          chartContent.parentNode.insertBefore(zoomControls, chartContent);

          // Set current zoom level
          let currentZoom = 1.0;

          // Get zoom control buttons
          const zoomInBtn = zoomControls.querySelector('.zoom-in');
          const zoomOutBtn = zoomControls.querySelector('.zoom-out');
          const zoomResetBtn = zoomControls.querySelector('.zoom-reset');

          // Add event listeners
          zoomInBtn.addEventListener('click', () => {
            if (currentZoom < 3.0) {
              currentZoom += 0.25;
              updateZoom();
            }
          });

          zoomOutBtn.addEventListener('click', () => {
            if (currentZoom > 0.5) {
              currentZoom -= 0.25;
              updateZoom();
            }
          });

          zoomResetBtn.addEventListener('click', () => {
            currentZoom = 1.0;
            updateZoom();
          });

          // Update zoom level
          function updateZoom() {
            try {
              // Try to access the iframe content
              const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
              const mermaidDiv = iframeDoc.querySelector('.mermaid');

              if (mermaidDiv) {
                mermaidDiv.style.transform = `scale(${currentZoom})`;
                mermaidDiv.style.transformOrigin = 'center top';
                mermaidDiv.style.display = 'block';
                mermaidDiv.style.margin = '0 auto';
              }

              // Update button states
              zoomInBtn.disabled = currentZoom >= 3.0;
              zoomOutBtn.disabled = currentZoom <= 0.5;

              console.log('Current iframe zoom level:', currentZoom);
            } catch (error) {
              console.error('Error updating iframe zoom:', error);
            }
          }

          // Initialize zoom when iframe loads
          iframe.addEventListener('load', updateZoom);

          // Set the standalone view link
          document.getElementById('view-standalone').href = `/api/render/page/${chart.id}`;

          // Hide loading message and show chart
          document.getElementById('loading-message').style.display = 'none';
          document.getElementById('chart-container').style.display = 'block';
        } catch (error) {
          console.error('Error rendering diagram:', error);
          document.getElementById('loading-message').style.display = 'none';
          document.getElementById('error-message').textContent = 'Error rendering diagram: ' + error.message;
          document.getElementById('error-message').style.display = 'block';
        }
      } catch (error) {
        console.error('Error loading shared chart:', error);
        document.getElementById('loading-message').style.display = 'none';
        document.getElementById('error-message').textContent = 'Error loading diagram: ' + error.message;
        document.getElementById('error-message').style.display = 'block';
      }
    }
  </script>
</body>
</html>
