<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FAQ - mermantic</title>

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">

  <!-- Stylesheets -->
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/nui/nui.css">
</head>
<body>
  <!-- Global Navigation Container -->
  <div id="navigation-container"></div>

  <main class="container">
    <section class="faq-header">
      <h2>Frequently Asked Questions</h2>
      <p>Find answers to common questions about using mermantic.</p>
    </section>

    <section class="faq-content">      <div class="faq-grid">
        <div class="faq-category">
          <h3>Understanding Mermaid</h3>
          <div class="faq-item">
            <h4>What is a Mermaid chart?</h4>
            <p>A Mermaid chart is a visual diagram that represents relationships, processes, and structures. It is commonly used for flowcharts, organizational charts, and Gantt charts. Mermaid charts are generated using the Mermaid JS syntax, allowing easy creation and customization.</p>
          </div>
          <div class="faq-item">
            <h4>What is Mermaid JS?</h4>
            <p>Mermaid JS is a JavaScript-based diagramming and charting tool that allows you to create diagrams using simple text syntax. It is an open-source tool that powers our free online chart generator. The syntax is designed to be both simple and powerful, making it accessible even for beginners.</p>
          </div>
          <div class="faq-item">
            <h4>How does Mermaid work?</h4>
            <p>Mermaid works by converting simple text descriptions into diagrams. Users define the structure of their diagram using a specific syntax, and the software generates a corresponding visual chart. This text-based approach makes it easy to version control your diagrams and integrate them with development workflows.</p>
          </div>
          <div class="faq-item">
            <h4>What are the benefits of using Mermaid?</h4>
            <p>Key benefits include:<br>
              • Easy-to-learn text-based syntax<br>
              • Version control support (store as text files in Git)<br>
              • Integration with development tools and platforms<br>
              • Wide variety of diagram types<br>
              • Free and open-source<br>
              • Excellent for technical documentation<br>
              • Support for collaboration</p>
          </div>
        </div>

        <div class="faq-category">
          <h3>Getting Started with mermantic</h3>
          <div class="faq-item">
            <h4>What is mermantic?</h4>
            <p>mermantic is a free web application that allows you to create, edit, and share Mermaid diagrams online. It provides a live preview of your diagrams, supports multiple diagram types, and offers features like saving, sharing, and collaboration.</p>
          </div>
          <div class="faq-item">
            <h4>Is it really free?</h4>
            <p>Yes! mermantic is completely free to use. You can create, save, and share as many diagrams as you want without any cost. All features, including templates, customization options, and export capabilities, are available at no charge.</p>
          </div>
          <div class="faq-item">
            <h4>Do I need an account?</h4>
            <p>While you can view shared diagrams without an account, creating a free account allows you to:<br>
              • Save diagrams for later use<br>
              • Organize diagrams in folders<br>
              • Share diagrams with others<br>
              • Access diagram templates<br>
              • Customize themes and settings<br>
              Registration is free and you can sign up using your email or Google account.</p>
          </div>
          <div class="faq-item">
            <h4>What diagram types can I create?</h4>
            <p>mermantic supports all Mermaid diagram types including:<br>
              • Flowcharts<br>
              • Sequence Diagrams<br>
              • Class Diagrams<br>
              • State Diagrams<br>
              • Entity Relationship Diagrams<br>
              • Gantt Charts<br>
              • Pie Charts<br>
              • User Journey Maps<br>
              • Git Graphs<br>
              • Mind Maps<br>
              • Timeline Charts</p>
          </div>
        </div>          <div class="faq-category">
          <h3>Creating and Customizing Diagrams</h3>
          <div class="faq-item">
            <h4>How do I create a Mermaid diagram online?</h4>
            <p>Creating a diagram is easy:<br>
              1. Log in to your account (it's free!)<br>
              2. Click the "Create New Diagram" button<br>
              3. Choose a template or start from scratch<br>
              4. Use the editor to write your diagram code<br>
              5. See your changes instantly in the live preview</p>
          </div>
          <div class="faq-item">
            <h4>Where can I find diagram templates?</h4>
            <p>We offer a variety of free templates in our Gallery section. You can:<br>
              • Browse templates by category<br>
              • Preview templates before using them<br>
              • Copy and customize any template<br>
              • Save modified templates for later use</p>
          </div>
          <div class="faq-item">
            <h4>Can I customize my diagrams?</h4>
            <p>Yes! You can customize:<br>
              • Colors and themes (Default, Dark, Forest, Base, Neutral)<br>
              • Fonts and text styles<br>
              • Line styles and arrows<br>
              • Layout and spacing<br>
              • Node shapes and sizes<br>
              Set preferences globally or per diagram.</p>
          </div>
          <div class="faq-item">
            <h4>How do I learn Mermaid syntax?</h4>
            <p>The editor supports several keyboard shortcuts:<br>
              • Ctrl+S / Cmd+S: Save diagram<br>
              • Ctrl+Enter / Cmd+Enter: Update preview<br>
              • F11: Toggle fullscreen<br>
              • Esc: Exit fullscreen or close modal</p>
          </div>
        </div>        <div class="faq-category">
          <h3>Advanced Features and Integration</h3>
          <div class="faq-item">
            <h4>Can I use Mermaid for collaborative projects?</h4>
            <p>Yes! Mermaid is excellent for collaboration:<br>
              • Share diagrams with unique URLs<br>
              • Embed diagrams in GitHub/GitLab<br>
              • Use in documentation platforms<br>
              • Store diagram code in version control<br>
              • Work together on technical documentation</p>
          </div>
          <div class="faq-item">
            <h4>Can I integrate Mermaid diagrams into websites?</h4>
            <p>Absolutely! You can:<br>
              • Embed diagrams in websites and blogs<br>
              • Integrate with content management systems<br>
              • Add dynamic, interactive diagrams<br>
              • Use Mermaid JS for custom integrations</p>
          </div>
          <div class="faq-item">
            <h4>Can AI read my Mermaid diagrams?</h4>
            <p>Yes! AI can analyze Mermaid diagrams to:<br>
              • Extract data and relationships<br>
              • Identify patterns and workflows<br>
              • Generate insights from diagram structure<br>
              • Assist with automation and process optimization</p>
          </div>
          <div class="faq-item">
            <h4>What are common use cases?</h4>
            <p>By default, your diagrams are private and only accessible to you. When you share a diagram, only people with the share link can view it. You can also make diagrams public if desired.</p>
          </div>
        </div>        <div class="faq-category">
          <h3>Saving, Sharing, and Export</h3>
          <div class="faq-item">
            <h4>Can I save my diagrams?</h4>
            <p>Yes! With a free account you can:<br>
              • Save unlimited diagrams<br>
              • Organize them in folders<br>
              • Access them from anywhere<br>
              • Keep track of versions<br>
              • Backup your work automatically</p>
          </div>
          <div class="faq-item">
            <h4>How do I share my diagrams?</h4>
            <p>Sharing is easy:<br>
              1. Click the "Share" button on any diagram<br>
              2. Copy the generated share link<br>
              3. Send the link to others - they can view without an account<br>
              4. Optionally make diagrams public<br>
              5. Control access permissions</p>
          </div>
          <div class="faq-item">
            <h4>What export options are available?</h4>
            <p>You can export your diagrams in multiple formats:<br>
              • PNG for raster images<br>
              • SVG for vector graphics<br>
              • Plain text for Mermaid syntax<br>
              • PDF for documentation<br>
              Perfect for presentations, documentation, and reports!</p>
          </div>
        </div>

        <div class="faq-category">
          <h3>Account and Support</h3>
          <div class="faq-item">
            <h4>How secure are my diagrams?</h4>
            <p>We take security seriously:<br>
              • Private diagrams are only accessible to you<br>
              • Shared diagrams require specific links<br>
              • Data is encrypted in transit and at rest<br>
              • Regular security updates<br>
              • Industry-standard protection</p>
          </div>
          <div class="faq-item">
            <h4>What if I need help?</h4>
            <p>We offer multiple support options:<br>
              • Comprehensive documentation<br>
              • Interactive tutorials<br>
              • Example templates<br>
              • Email support<br>
              • Community forums</p>
          </div>
          <div class="faq-item">
            <h4>Can I use Mermaid for non-technical projects?</h4>
            <p>Absolutely! While Mermaid is popular in technical fields, it's great for:<br>
              • Project planning<br>
              • Organization charts<br>
              • Process documentation<br>
              • Educational materials<br>
              • Visual storytelling<br>
              Our templates and user-friendly interface make it accessible to everyone!</p>
          </div>
        </div>
      </div>
    </section>

    <section class="faq-footer">
      <div class="support-info">
        <h3>Still have questions?</h3>
        <p>Contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a></p>
      </div>
    </section>
  </main>

  <!-- Global Footer Container -->
  <div id="footer-container"></div>

  <!-- Component Scripts -->
  <script src="/js/components/navigation.js"></script>
  <script src="/js/components/footer.js"></script>
  <script src="/js/components/analytics-manager.js"></script>
  <script src="/js/components/seo-manager.js"></script>
  <script src="/js/components/component-loader.js"></script>

  <script src="/nui/nui.js"></script>
  <script src="/js/auth.js"></script>
  <script>
    // Initialize component loader
    document.addEventListener('DOMContentLoaded', () => {
      const componentLoader = new ComponentLoader();
      componentLoader.init();
    });
  </script>
</body>
</html>
