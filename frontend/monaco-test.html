<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Monaco Editor Test - Mermantic</title>
  <link rel="stylesheet" href="/css/styles.css">
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: var(--background-color);
      color: var(--text-color);
    }
    
    .test-container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .test-section {
      margin-bottom: 2rem;
      padding: 1rem;
      background: var(--surface-elevated);
      border-radius: var(--border-radius);
      border: 1px solid var(--border-color);
    }
    
    .editor-test {
      display: flex;
      gap: 1rem;
      height: 400px;
    }
    
    .editor-panel, .preview-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: var(--background-secondary);
      border-radius: var(--border-radius);
      border: 1px solid var(--border-color);
    }
    
    .panel-header {
      padding: 0.5rem 1rem;
      background: var(--gradient-surface);
      border-bottom: 1px solid var(--border-color);
      font-weight: 600;
    }
    
    .panel-content {
      flex: 1;
      padding: 0;
      overflow: hidden;
    }
    
    #test-editor {
      height: 100%;
      width: 100%;
    }
    
    #test-preview {
      height: 100%;
      padding: 1rem;
      overflow: auto;
    }
    
    .error-message {
      color: #ff6b6b;
      background: rgba(255, 107, 107, 0.1);
      padding: 1rem;
      border-radius: 4px;
      border: 1px solid rgba(255, 107, 107, 0.3);
    }
    
    .success-message {
      color: #51cf66;
      background: rgba(81, 207, 102, 0.1);
      padding: 1rem;
      border-radius: 4px;
      border: 1px solid rgba(81, 207, 102, 0.3);
    }
    
    .test-controls {
      margin-bottom: 1rem;
      display: flex;
      gap: 1rem;
      align-items: center;
    }
    
    .test-button {
      padding: 0.5rem 1rem;
      background: var(--primary-color);
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .test-button:hover {
      background: var(--primary-hover);
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>Monaco Editor Integration Test</h1>
    
    <div class="test-section">
      <h2>Monaco Editor Status</h2>
      <div id="status-message">Loading...</div>
    </div>
    
    <div class="test-section">
      <h2>Monaco Editor Test</h2>
      <div class="test-controls">
        <button class="test-button" onclick="testCopyFunction()">Test Copy</button>
        <button class="test-button" onclick="testPersistence()">Test Persistence</button>
        <button class="test-button" onclick="clearStorage()">Clear Storage</button>
      </div>
      
      <div class="editor-test">
        <div class="editor-panel">
          <div class="panel-header">Monaco Editor</div>
          <div class="panel-content">
            <div id="test-editor"></div>
          </div>
        </div>
        
        <div class="preview-panel">
          <div class="panel-header">Mermaid Preview</div>
          <div class="panel-content">
            <div id="test-preview"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Load required scripts -->
  <script src="/js/mermaid.min.js"></script>
  <script src="/js/monaco-mermaid-editor.js"></script>
  <script src="/js/monaco-integration.js"></script>
  
  <script>
    let testEditor = null;
    
    // Initialize Mermaid
    mermaid.initialize({
      startOnLoad: false,
      securityLevel: 'loose',
      theme: 'dark',
      logLevel: 'error',
      fontFamily: 'monospace',
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'basis'
      }
    });
    
    // Test Monaco Editor initialization
    async function initializeTest() {
      const statusDiv = document.getElementById('status-message');
      
      try {
        // Check if Monaco is supported
        if (!MonacoMermaidEditor.isSupported()) {
          statusDiv.innerHTML = '<div class="error-message">Monaco Editor is not supported in this browser</div>';
          return;
        }
        
        statusDiv.innerHTML = '<div class="success-message">Monaco Editor is supported</div>';
        
        // Create Monaco Editor
        testEditor = new MonacoMermaidEditor('test-editor', {
          value: `graph TD
    A[Monaco Editor] --> B{Working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    D --> B
    C --> E[Test Complete]`
        });
        
        // Wait for editor to initialize
        await new Promise(resolve => {
          const checkInit = () => {
            if (testEditor.isInitialized) {
              resolve();
            } else {
              setTimeout(checkInit, 100);
            }
          };
          checkInit();
        });
        
        // Connect to preview
        testEditor.setOnContentChange((content) => {
          MonacoIntegration.updateMermaidPreview(
            document.getElementById('test-preview'), 
            content
          );
        });
        
        // Initial preview update
        MonacoIntegration.updateMermaidPreview(
          document.getElementById('test-preview'), 
          testEditor.getValue()
        );
        
        statusDiv.innerHTML += '<div class="success-message">Monaco Editor initialized successfully!</div>';
        
      } catch (error) {
        console.error('Test initialization failed:', error);
        statusDiv.innerHTML += `<div class="error-message">Initialization failed: ${error.message}</div>`;
      }
    }
    
    // Test copy functionality
    async function testCopyFunction() {
      if (!testEditor) {
        alert('Editor not initialized');
        return;
      }
      
      try {
        await navigator.clipboard.writeText(testEditor.getValue());
        alert('Copy test successful!');
      } catch (error) {
        alert('Copy test failed: ' + error.message);
      }
    }
    
    // Test persistence
    function testPersistence() {
      if (!testEditor) {
        alert('Editor not initialized');
        return;
      }
      
      const testContent = `graph LR
    Test[Persistence Test] --> Works[It Works!]`;
      
      testEditor.setValue(testContent);
      alert('Content set. Refresh the page to test if it persists.');
    }
    
    // Clear storage
    function clearStorage() {
      if (testEditor) {
        testEditor.clearPersistedContent();
        alert('Storage cleared');
      }
    }
    
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', initializeTest);
  </script>
</body>
</html>
