<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reset Password - mermantic</title>
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/nui/nui.css">
</head>
<body>
  <!-- Global Navigation Container -->
  <div id="navigation-container"></div>

  <main class="container">
    <section class="auth-form">
      <h2>Set New Password</h2>
      <p id="username-display"></p>

      <div id="error-message" class="error-message"></div>
      <div id="success-message" class="success-message" style="display: none;"></div>
      <div id="loading-message" style="display: none;">Verifying reset token...</div>

      <form id="reset-password-form" style="display: none;">
        <div class="form-group">
          <label for="password">New Password</label>
          <input type="password" id="password" name="password" class="nui-input" required minlength="6">
        </div>

        <div class="form-group">
          <label for="confirm-password">Confirm New Password</label>
          <input type="password" id="confirm-password" name="confirm-password" class="nui-input" required minlength="6">
        </div>

        <div class="form-actions">
          <button type="submit" class="nui-button primary">Reset Password</button>
        </div>
      </form>

      <p class="auth-redirect">
        <a href="/login.html">Back to Login</a>
      </p>
    </section>
  </main>

  <!-- Global Footer Container -->
  <div id="footer-container"></div>

  <!-- Component Scripts -->
  <script src="/js/components/navigation.js"></script>
  <script src="/js/components/footer.js"></script>
  <script src="/js/components/component-loader.js"></script>

  <script src="/nui/nui.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const resetPasswordForm = document.getElementById('reset-password-form');
      const errorMessage = document.getElementById('error-message');
      const successMessage = document.getElementById('success-message');
      const loadingMessage = document.getElementById('loading-message');
      const usernameDisplay = document.getElementById('username-display');

      // Get token from URL
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get('token');

      if (!token) {
        errorMessage.textContent = 'Invalid reset link. Please request a new password reset.';
        errorMessage.style.display = 'block';
        return;
      }

      // Verify token on page load
      verifyToken(token);

      async function verifyToken(token) {
        loadingMessage.style.display = 'block';

        try {
          const response = await fetch(`/api/users/verify-reset-token/${token}`);
          const data = await response.json();

          loadingMessage.style.display = 'none';

          if (!response.ok) {
            throw new Error(data.message || 'Invalid or expired reset token');
          }

          // Token is valid, show the form
          usernameDisplay.textContent = `Reset password for: ${data.username}`;
          resetPasswordForm.style.display = 'block';

        } catch (error) {
          loadingMessage.style.display = 'none';
          errorMessage.textContent = error.message;
          errorMessage.style.display = 'block';
        }
      }

      resetPasswordForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm-password').value;

        // Hide previous messages
        errorMessage.style.display = 'none';
        successMessage.style.display = 'none';

        // Validate passwords match
        if (password !== confirmPassword) {
          errorMessage.textContent = 'Passwords do not match';
          errorMessage.style.display = 'block';
          return;
        }

        // Validate password length
        if (password.length < 6) {
          errorMessage.textContent = 'Password must be at least 6 characters long';
          errorMessage.style.display = 'block';
          return;
        }

        try {
          const response = await fetch('/api/users/reset-password', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ token, password })
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.message || 'Failed to reset password');
          }

          // Show success message
          successMessage.textContent = 'Password reset successfully! Redirecting to login...';
          successMessage.style.display = 'block';

          // Hide the form
          resetPasswordForm.style.display = 'none';

          // Redirect to login after 3 seconds
          setTimeout(() => {
            window.location.href = '/login.html?reset=success';
          }, 3000);

        } catch (error) {
          errorMessage.textContent = error.message;
          errorMessage.style.display = 'block';
        }
      });
    });
  </script>
</body>
</html>
