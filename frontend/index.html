<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>mermantic - Create and Share Mermaid Diagrams</title>

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">

  <!-- Stylesheets -->
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/nui/nui.css">
</head>
<body>
  <!-- Global Navigation Container -->
  <div id="navigation-container"></div>

  <main class="container">
    <section class="hero">
      <h2>Create, Edit, and Share Mermaid Diagrams for free</h2>
      <p>A online editor for creating and sharing diagrams using Mermaid syntax.</p>
      <div class="cta-buttons">
        <a href="/register.html" class="nui-button primary">Get Started</a>
        <a href="#demo" class="nui-button secondary">See Demo</a>
      </div>
    </section>

    <section class="video-section">
      <h3>Watch How It Works</h3>
      <div class="video-container">
        <iframe 
        width="860" 
        height="485" 
        src="https://www.youtube.com/embed/U0RYDmOl1WM?modestbranding=1&rel=0" 
        title="Introduction to mermantic" 
        frameborder="0" 
        allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" 
        allowfullscreen>
      </iframe>
      </div>
    </section>

    <section id="demo" class="demo-section">
      <h3>Try it out</h3>
      <div class="editor-container">
        <div class="editor-panel">
          <h4>Mermaid Syntax</h4>
          <textarea id="demo-editor" class="mermaid-editor">journey
    title User Journey for New Signup
    section Sign Up
      Visit Site: 5: User
      Click Sign Up: 4: User
      Fill Form: 3: User</textarea>
        </div>
        <div class="preview-panel">
          <h4>Preview</h4>
          <div id="demo-preview" class="mermaid-preview"></div>
        </div>
      </div>
    </section>

    <section class="features">
      <h3>Features</h3>
      <div class="feature-grid">
        <div class="feature-card">
          <h4>Live Preview</h4>
          <p>See your diagrams render in real-time as you type.</p>
        </div>
        <div class="feature-card">
          <h4>Save & Share</h4>
          <p>Save your diagrams and share them with unique URLs.</p>
        </div>
        <div class="feature-card">
          <h4>User Dashboard</h4>
          <p>Manage all your diagrams in one place.</p>
        </div>
        <div class="feature-card">
          <h4>Multiple Diagram Types</h4>
          <p>Create flowcharts, sequence diagrams, class diagrams, and more.</p>
        </div>
      </div>
    </section>
  </main>

  <!-- Global Footer Container -->
  <div id="footer-container"></div>

  <!-- Component Scripts -->
  <script src="/js/components/navigation.js"></script>
  <script src="/js/components/footer.js"></script>
  <script src="/js/components/analytics-manager.js"></script>
  <script src="/js/components/seo-manager.js"></script>
  <script src="/js/components/component-loader.js"></script>

  <script src="/js/mermaid.min.js"></script>
  <script src="/nui/nui.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/auth.js"></script>
  <script src="/js/monaco-mermaid-editor.js"></script>
  <script src="/js/monaco-integration.js"></script>
  <script>
    // Initialize Mermaid with better error handling
    mermaid.initialize({
      startOnLoad: false,
      securityLevel: 'loose',
      theme: 'dark', // Use dark theme for better visibility on dark background
      logLevel: 'error', // Change to 'error' to hide warnings
      fontFamily: 'monospace',
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'basis'
      },
      er: {
        useMaxWidth: true
      },
      sequence: {
        useMaxWidth: true,
        wrap: true,
        showSequenceNumbers: false
      },
      gantt: {
        useMaxWidth: true
      },
      journey: {
        useMaxWidth: true
      },
      // Suppress errors in the console
      suppressErrors: true
    });

    // Monaco Editor initialization for demo
    document.addEventListener('DOMContentLoaded', async function() {
      try {
        // Initialize Monaco Editor for the demo
        await MonacoIntegration.initializeDemoEditor();

        // Add copy button to the demo editor header
        setTimeout(() => {
          MonacoIntegration.addCopyButton('demo-editor', '.editor-header h4');
        }, 1000);

      } catch (error) {
        console.error('Failed to initialize Monaco Editor, falling back to textarea:', error);
        // Fallback to original textarea functionality if Monaco fails
        const demoEditor = document.getElementById('demo-editor');
        const demoPreview = document.getElementById('demo-preview');

        if (demoEditor && demoPreview) {
          function updatePreview() {
            MonacoIntegration.updateMermaidPreview(demoPreview, demoEditor.value);
          }

          let debounceTimer;
          demoEditor.addEventListener('input', function() {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(updatePreview, 300);
          });

          updatePreview();
        }
      }
    });
  </script>
</body>
</html>
