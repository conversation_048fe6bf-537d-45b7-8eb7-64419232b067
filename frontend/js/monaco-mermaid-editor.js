/**
 * Monaco Editor Integration for Mermaid
 * Provides a Monaco Editor-based replacement for textarea Mermaid editors
 */

class MonacoMermaidEditor {
  constructor(containerId, options = {}) {
    this.containerId = containerId;
    this.container = document.getElementById(containerId);
    this.editor = null;
    this.previewContainer = null;
    this.isInitialized = false;

    // Default options
    this.options = {
      theme: 'vs-dark',
      language: 'mermaid',
      automaticLayout: true,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      lineNumbers: 'on',
      fontSize: 14,
      fontFamily: 'Fira Code, Consolas, monospace',
      tabSize: 2,
      insertSpaces: true,
      renderWhitespace: 'selection',
      folding: true,
      showFoldingControls: 'always',
      ...options
    };

    // Debounce settings
    this.debounceDelay = 300;
    this.debounceTimer = null;

    // Preview callback
    this.onContentChange = null;

    // Storage key for persistence
    this.storageKey = `mermaid-editor-${containerId}`;

    this.init();
  }

  async init() {
    if (!this.container) {
      console.error(`Container with id '${this.containerId}' not found`);
      return;
    }

    try {
      // Load Monaco Editor if not already loaded
      await this.loadMonaco();

      // Register Mermaid language
      this.registerMermaidLanguage();

      // Create editor
      this.createEditor();

      // Setup event listeners
      this.setupEventListeners();

      // Load persisted content
      this.loadPersistedContent();

      this.isInitialized = true;

      // Trigger initial content change
      if (this.onContentChange) {
        this.onContentChange(this.getValue());
      }

    } catch (error) {
      console.error('Failed to initialize Monaco Editor:', error);
      this.fallbackToTextarea();
    }
  }

  async loadMonaco() {
    // Check if Monaco is already loaded
    if (window.monaco) {
      return;
    }

    return new Promise((resolve, reject) => {
      // Load Monaco Editor from CDN
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/loader.js';
      script.onload = () => {
        require.config({
          paths: {
            'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs'
          }
        });

        require(['vs/editor/editor.main'], () => {
          resolve();
        }, (error) => {
          reject(error);
        });
      };
      script.onerror = () => reject(new Error('Failed to load Monaco Editor'));
      document.head.appendChild(script);
    });
  }

  registerMermaidLanguage() {
    if (!window.monaco) return;

    // Register the Mermaid language
    monaco.languages.register({ id: 'mermaid' });

    // Define Mermaid syntax highlighting
    monaco.languages.setMonarchTokensProvider('mermaid', {
      tokenizer: {
        root: [
          // Diagram types
          [/\b(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|erDiagram|journey|gantt|pie|gitgraph|mindmap|timeline|sankey|xyChart|quadrantChart|requirement|c4Context|c4Container|c4Component|c4Dynamic)\b/, 'keyword'],

          // Direction keywords
          [/\b(TD|TB|BT|RL|LR|subgraph|end)\b/, 'keyword.control'],

          // Arrows and connections
          [/-->|->|---|-|==>|==|\.->|\.-|~~>|~|<->|<-->/, 'operator'],

          // Node shapes and labels
          [/\[([^\]]*)\]/, 'string'],
          [/\(([^)]*)\)/, 'string'],
          [/\{([^}]*)\}/, 'string'],
          [/\(\(([^)]*)\)\)/, 'string'],
          [/>([^<]*)</, 'string'],

          // Participant and actor keywords
          [/\b(participant|actor|note|activate|deactivate|loop|alt|else|opt|par|and|rect|autonumber)\b/, 'keyword.control'],

          // Class diagram keywords
          [/\b(class|interface|enum|abstract|static|public|private|protected)\b/, 'keyword.control'],

          // State diagram keywords
          [/\b(state|choice|fork|join|concurrent)\b/, 'keyword.control'],

          // ER diagram keywords
          [/\b(entity|relationship|attribute|key)\b/, 'keyword.control'],

          // Journey keywords
          [/\b(title|section)\b/, 'keyword.control'],

          // Comments
          [/%%.*$/, 'comment'],

          // Strings
          [/"([^"\\]|\\.)*$/, 'string.invalid'],
          [/"/, 'string', '@string'],
          [/'([^'\\]|\\.)*$/, 'string.invalid'],
          [/'/, 'string', '@string_single'],

          // Numbers
          [/\d+/, 'number'],

          // Identifiers
          [/[a-zA-Z_]\w*/, 'identifier'],

          // Whitespace
          [/[ \t\r\n]+/, 'white'],
        ],

        string: [
          [/[^\\"]+/, 'string'],
          [/\\./, 'string.escape'],
          [/"/, 'string', '@pop']
        ],

        string_single: [
          [/[^\\']+/, 'string'],
          [/\\./, 'string.escape'],
          [/'/, 'string', '@pop']
        ]
      }
    });

    // Define theme colors for Mermaid
    monaco.editor.defineTheme('mermaid-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'keyword', foreground: '569cd6', fontStyle: 'bold' },
        { token: 'keyword.control', foreground: 'c586c0', fontStyle: 'bold' },
        { token: 'operator', foreground: 'd4d4d4', fontStyle: 'bold' },
        { token: 'string', foreground: 'ce9178' },
        { token: 'comment', foreground: '6a9955', fontStyle: 'italic' },
        { token: 'number', foreground: 'b5cea8' },
        { token: 'identifier', foreground: '9cdcfe' }
      ],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4'
      }
    });

    // Set the theme
    if (this.options.theme === 'vs-dark') {
      this.options.theme = 'mermaid-dark';
    }
  }

  createEditor() {
    if (!window.monaco) {
      throw new Error('Monaco Editor not loaded');
    }

    // Create the editor
    this.editor = monaco.editor.create(this.container, {
      value: this.getDefaultContent(),
      ...this.options
    });

    // Set initial size
    this.editor.layout();
  }

  setupEventListeners() {
    if (!this.editor) return;

    // Listen for content changes
    this.editor.onDidChangeModelContent(() => {
      this.debouncedContentChange();
      this.persistContent();
    });

    // Handle window resize
    window.addEventListener('resize', () => {
      if (this.editor) {
        this.editor.layout();
      }
    });
  }

  debouncedContentChange() {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = setTimeout(() => {
      if (this.onContentChange) {
        this.onContentChange(this.getValue());
      }
    }, this.debounceDelay);
  }

  getDefaultContent() {
    return `graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    D --> B`;
  }

  getValue() {
    return this.editor ? this.editor.getValue() : '';
  }

  setValue(value) {
    if (this.editor) {
      this.editor.setValue(value);
    }
  }

  setOnContentChange(callback) {
    this.onContentChange = callback;
  }

  persistContent() {
    if (this.editor) {
      try {
        localStorage.setItem(this.storageKey, this.getValue());
      } catch (error) {
        console.warn('Failed to persist editor content:', error);
      }
    }
  }

  loadPersistedContent() {
    try {
      const content = localStorage.getItem(this.storageKey);
      if (content && this.editor) {
        this.editor.setValue(content);
      }
    } catch (error) {
      console.warn('Failed to load persisted content:', error);
    }
  }

  clearPersistedContent() {
    try {
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.warn('Failed to clear persisted content:', error);
    }
  }

  focus() {
    if (this.editor) {
      this.editor.focus();
    }
  }

  layout() {
    if (this.editor) {
      this.editor.layout();
    }
  }

  dispose() {
    if (this.editor) {
      this.editor.dispose();
      this.editor = null;
    }
    this.isInitialized = false;
  }

  fallbackToTextarea() {
    console.warn('Falling back to textarea editor');

    // Create a textarea as fallback
    const textarea = document.createElement('textarea');
    textarea.className = 'mermaid-editor';
    textarea.value = this.getDefaultContent();
    textarea.style.width = '100%';
    textarea.style.height = '100%';
    textarea.style.border = 'none';
    textarea.style.outline = 'none';
    textarea.style.resize = 'none';
    textarea.style.fontFamily = 'Fira Code, Consolas, monospace';
    textarea.style.fontSize = '14px';
    textarea.style.padding = '1rem';
    textarea.style.backgroundColor = '#1e1e1e';
    textarea.style.color = '#d4d4d4';

    // Clear container and add textarea
    this.container.innerHTML = '';
    this.container.appendChild(textarea);

    // Setup event listeners for textarea
    let debounceTimer;
    textarea.addEventListener('input', () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      debounceTimer = setTimeout(() => {
        if (this.onContentChange) {
          this.onContentChange(textarea.value);
        }
      }, this.debounceDelay);
    });

    // Override methods to work with textarea
    this.getValue = () => textarea.value;
    this.setValue = (value) => { textarea.value = value; };
    this.focus = () => textarea.focus();
    this.layout = () => { }; // No-op for textarea
  }

  // Static method to check if Monaco is supported
  static isSupported() {
    return typeof window !== 'undefined' &&
      !window.navigator.userAgent.includes('MSIE') &&
      !window.navigator.userAgent.includes('Trident');
  }

  // Static method to create editor with copy button
  static createWithCopyButton(containerId, options = {}) {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error(`Container with id '${containerId}' not found`);
      return null;
    }

    // Create wrapper for editor and copy button
    const wrapper = document.createElement('div');
    wrapper.style.position = 'relative';
    wrapper.style.height = '100%';
    wrapper.style.width = '100%';

    // Create editor container
    const editorContainer = document.createElement('div');
    editorContainer.style.height = '100%';
    editorContainer.style.width = '100%';
    editorContainer.id = containerId + '-editor';

    // Create copy button
    const copyButton = document.createElement('button');
    copyButton.innerHTML = '📋 Copy';
    copyButton.className = 'copy-button';
    copyButton.style.position = 'absolute';
    copyButton.style.top = '10px';
    copyButton.style.right = '10px';
    copyButton.style.zIndex = '1000';
    copyButton.style.padding = '5px 10px';
    copyButton.style.backgroundColor = '#007acc';
    copyButton.style.color = 'white';
    copyButton.style.border = 'none';
    copyButton.style.borderRadius = '3px';
    copyButton.style.cursor = 'pointer';
    copyButton.style.fontSize = '12px';

    // Replace container content
    container.innerHTML = '';
    wrapper.appendChild(editorContainer);
    wrapper.appendChild(copyButton);
    container.appendChild(wrapper);

    // Create editor
    const editor = new MonacoMermaidEditor(editorContainer.id, options);

    // Setup copy button functionality
    copyButton.addEventListener('click', async () => {
      try {
        await navigator.clipboard.writeText(editor.getValue());
        copyButton.innerHTML = '✓ Copied!';
        copyButton.style.backgroundColor = '#28a745';
        setTimeout(() => {
          copyButton.innerHTML = '📋 Copy';
          copyButton.style.backgroundColor = '#007acc';
        }, 2000);
      } catch (error) {
        console.error('Failed to copy to clipboard:', error);
        copyButton.innerHTML = '❌ Failed';
        copyButton.style.backgroundColor = '#dc3545';
        setTimeout(() => {
          copyButton.innerHTML = '📋 Copy';
          copyButton.style.backgroundColor = '#007acc';
        }, 2000);
      }
    });

    return editor;
  }
}

// Export for use in other scripts
window.MonacoMermaidEditor = MonacoMermaidEditor;
