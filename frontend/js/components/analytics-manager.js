// Analytics Manager - Centralized analytics handling
class AnalyticsManager {
  constructor() {
    this.isInitialized = false;
    this.isEnabled = false;
    this.trackingId = 'G-QP1HKW0RS9';
    this.debugMode = false;
    
    // Check if we're in development mode
    this.isDevelopment = window.location.hostname === 'localhost' || 
                        window.location.hostname === '127.0.0.1' ||
                        window.location.hostname.includes('localhost');
    
    // Enable debug mode in development
    this.debugMode = this.isDevelopment;
    
    // Check user consent (you can expand this for GDPR compliance)
    this.checkConsent();
  }

  // Check if user has consented to analytics
  checkConsent() {
    // For now, we'll assume consent unless user has opted out
    // You can expand this to show a cookie banner and get explicit consent
    const hasOptedOut = localStorage.getItem('analytics-opt-out') === 'true';
    this.isEnabled = !hasOptedOut && !this.isDevelopment;
    
    if (this.debugMode) {
      console.log('🔍 Analytics Debug Mode - tracking disabled in development');
    }
  }

  // Initialize Google Analytics
  async init() {
    if (this.isInitialized || !this.isEnabled) {
      return;
    }

    try {
      // Load gtag script
      await this.loadGtagScript();
      
      // Initialize dataLayer
      window.dataLayer = window.dataLayer || [];
      window.gtag = function() {
        dataLayer.push(arguments);
      };
      
      // Configure gtag
      gtag('js', new Date());
      gtag('config', this.trackingId, {
        // Enhanced privacy settings
        anonymize_ip: true,
        allow_google_signals: false,
        allow_ad_personalization_signals: false,
        // Custom settings for your app
        page_title: document.title,
        page_location: window.location.href
      });

      this.isInitialized = true;
      
      if (this.debugMode) {
        console.log('✅ Analytics initialized with tracking ID:', this.trackingId);
      }
      
      // Track initial page view
      this.trackPageView();
      
    } catch (error) {
      console.error('❌ Failed to initialize analytics:', error);
    }
  }

  // Load the gtag script dynamically
  loadGtagScript() {
    return new Promise((resolve, reject) => {
      // Check if script is already loaded
      if (document.querySelector(`script[src*="googletagmanager.com/gtag/js"]`)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${this.trackingId}`;
      
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Google Analytics script'));
      
      document.head.appendChild(script);
    });
  }

  // Track page views
  trackPageView(pagePath = null, pageTitle = null) {
    if (!this.isEnabled || !this.isInitialized) {
      if (this.debugMode) {
        console.log('📊 [DEBUG] Page view:', pagePath || window.location.pathname);
      }
      return;
    }

    gtag('config', this.trackingId, {
      page_path: pagePath || window.location.pathname,
      page_title: pageTitle || document.title
    });

    if (this.debugMode) {
      console.log('📊 Page view tracked:', pagePath || window.location.pathname);
    }
  }

  // Track custom events
  trackEvent(eventName, parameters = {}) {
    if (!this.isEnabled || !this.isInitialized) {
      if (this.debugMode) {
        console.log('📊 [DEBUG] Event:', eventName, parameters);
      }
      return;
    }

    gtag('event', eventName, parameters);

    if (this.debugMode) {
      console.log('📊 Event tracked:', eventName, parameters);
    }
  }

  // Convenience methods for common events
  trackChartCreated(chartType = 'unknown') {
    this.trackEvent('chart_created', {
      chart_type: chartType,
      event_category: 'engagement'
    });
  }

  trackChartShared(shareMethod = 'link') {
    this.trackEvent('chart_shared', {
      share_method: shareMethod,
      event_category: 'engagement'
    });
  }

  trackDownload(fileType = 'unknown') {
    this.trackEvent('file_download', {
      file_type: fileType,
      event_category: 'engagement'
    });
  }

  trackUserRegistration(method = 'email') {
    this.trackEvent('sign_up', {
      method: method,
      event_category: 'user_engagement'
    });
  }

  trackUserLogin(method = 'email') {
    this.trackEvent('login', {
      method: method,
      event_category: 'user_engagement'
    });
  }

  // Privacy methods
  optOut() {
    localStorage.setItem('analytics-opt-out', 'true');
    this.isEnabled = false;
    
    // Disable Google Analytics
    if (window.gtag) {
      gtag('config', this.trackingId, {
        send_page_view: false
      });
    }
    
    console.log('🔒 Analytics tracking disabled');
  }

  optIn() {
    localStorage.removeItem('analytics-opt-out');
    this.isEnabled = true;
    
    // Reinitialize if needed
    if (!this.isInitialized) {
      this.init();
    }
    
    console.log('📊 Analytics tracking enabled');
  }

  // Get current consent status
  getConsentStatus() {
    return {
      isEnabled: this.isEnabled,
      hasOptedOut: localStorage.getItem('analytics-opt-out') === 'true',
      isDevelopment: this.isDevelopment
    };
  }
}

// Export for use in other scripts
window.AnalyticsManager = AnalyticsManager;
