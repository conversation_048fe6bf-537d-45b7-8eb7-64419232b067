{"name": "mermantic", "version": "1.0.0", "description": "A web application for creating and sharing Mermaid diagrams", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "test": "echo \"Error: no test specified\" && exit 1", "test:google-auth": "node test-google-auth.js", "test:password-reset": "node test-password-reset.js", "test:chart-saving": "node test-chart-saving.js", "rebuild": "npm rebuild", "clean-install": "rm -rf node_modules package-lock.json && npm install", "setup:windows": "npm run clean-install", "setup:linux": "npm run clean-install", "postinstall": "node -e \"try { require('./scripts/check-platform.js').checkPlatform(); } catch(e) { console.log('Platform check skipped'); }\"", "check-platform": "node scripts/check-platform.js"}, "keywords": ["mermaid", "diagrams", "charts"], "author": "", "license": "MIT", "dependencies": {"bcrypt": "^6.0.0", "better-sqlite3": "^11.10.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-session": "^1.17.3", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "resend": "^4.5.1"}, "devDependencies": {"nodemon": "^3.1.10"}}