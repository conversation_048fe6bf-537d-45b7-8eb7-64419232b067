# Server Configuration
PORT=3000
SESSION_SECRET=your_session_secret_here

# Google OAuth Configuration
# To set up Google OAuth:
# 1. Go to https://console.developers.google.com/
# 2. Create a new project or select an existing one
# 3. Enable the Google+ API
# 4. Create OAuth 2.0 credentials
# 5. Add your domains to authorized origins:
#    - Development: http://localhost:3000
#    - Production: https://yourdomain.com
# 6. Add the callback URLs to authorized redirect URIs:
#    - Development: http://localhost:3000/api/auth/google/callback
#    - Production: https://yourdomain.com/api/auth/google/callback

GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# Note: Callback URL is automatically determined based on NODE_ENV
# Development: http://localhost:3000/api/auth/google/callback
# Production: https://yourdomain.com/api/auth/google/callback

# Email Configuration (for password reset)
RESEND_API_KEY=your_resend_api_key_here
