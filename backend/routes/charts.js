const express = require('express');
const router = express.Router();
const chartController = require('../controllers/chartController');
const { isAuthenticated } = require('../middleware/auth');

// Create a new chart
router.post('/', isAuthenticated, chartController.createChart);

// Get all charts for current user
router.get('/', isAuthenticated, chartController.getUserCharts);

// Get all folders for current user
router.get('/folders/list', isAuthenticated, chartController.getUserFolders);

// Get a chart by ID
router.get('/:id', chartController.getChartById);

// Get a chart by share ID
router.get('/share/:shareId', chartController.getChartByShareId);

// Update a chart
router.put('/:id', isAuthenticated, chartController.updateChart);

// Delete a chart
router.delete('/:id', isAuthenticated, chartController.deleteChart);

// Debug endpoint to check session
router.get('/debug/session', (req, res) => {
  console.log('Session debug request');
  console.log('Session:', req.session);
  console.log('User in session:', req.session?.user);

  res.json({
    hasSession: !!req.session,
    hasUser: !!req.session?.user,
    user: req.session?.user,
    sessionId: req.sessionID
  });
});

module.exports = router;
