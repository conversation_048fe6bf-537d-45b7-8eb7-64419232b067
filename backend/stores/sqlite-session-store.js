const { Store } = require('express-session');

/**
 * Custom SQLite session store for express-session
 * Uses the existing better-sqlite3 database instance to avoid dependency conflicts
 */
class SQLiteSessionStore extends Store {
  constructor(options = {}) {
    super(options);
    
    if (!options.db) {
      throw new Error('SQLiteSessionStore requires a database instance');
    }
    
    this.db = options.db;
    this.tableName = options.tableName || 'sessions';
    this.cleanupInterval = options.cleanupInterval || 15 * 60 * 1000; // 15 minutes
    
    // Start periodic cleanup of expired sessions
    this.startCleanup();
    
    console.log('✅ SQLite session store initialized');
  }

  /**
   * Get a session from the database
   */
  get(sid, callback) {
    try {
      const stmt = this.db.db.prepare(`
        SELECT sess FROM ${this.tableName} 
        WHERE sid = ? AND expire > datetime('now')
      `);
      const result = stmt.get(sid);
      
      if (result) {
        const session = JSON.parse(result.sess);
        callback(null, session);
      } else {
        callback(null, null);
      }
    } catch (error) {
      console.error('SQLiteSessionStore get error:', error);
      callback(error);
    }
  }

  /**
   * Set/update a session in the database
   */
  set(sid, session, callback) {
    try {
      const maxAge = session.cookie?.maxAge || 86400000; // Default 24 hours
      const expire = new Date(Date.now() + maxAge);
      
      const stmt = this.db.db.prepare(`
        INSERT OR REPLACE INTO ${this.tableName} (sid, sess, expire)
        VALUES (?, ?, ?)
      `);
      
      stmt.run(sid, JSON.stringify(session), expire.toISOString());
      
      if (callback) callback(null);
    } catch (error) {
      console.error('SQLiteSessionStore set error:', error);
      if (callback) callback(error);
    }
  }

  /**
   * Destroy a session from the database
   */
  destroy(sid, callback) {
    try {
      const stmt = this.db.db.prepare(`DELETE FROM ${this.tableName} WHERE sid = ?`);
      stmt.run(sid);
      
      if (callback) callback(null);
    } catch (error) {
      console.error('SQLiteSessionStore destroy error:', error);
      if (callback) callback(error);
    }
  }

  /**
   * Get the number of active sessions
   */
  length(callback) {
    try {
      const stmt = this.db.db.prepare(`
        SELECT COUNT(*) as count FROM ${this.tableName} 
        WHERE expire > datetime('now')
      `);
      const result = stmt.get();
      
      callback(null, result.count);
    } catch (error) {
      console.error('SQLiteSessionStore length error:', error);
      callback(error);
    }
  }

  /**
   * Clear all sessions from the database
   */
  clear(callback) {
    try {
      const stmt = this.db.db.prepare(`DELETE FROM ${this.tableName}`);
      stmt.run();
      
      if (callback) callback(null);
    } catch (error) {
      console.error('SQLiteSessionStore clear error:', error);
      if (callback) callback(error);
    }
  }

  /**
   * Touch a session to update its expiration time
   */
  touch(sid, session, callback) {
    try {
      const maxAge = session.cookie?.maxAge || 86400000; // Default 24 hours
      const expire = new Date(Date.now() + maxAge);
      
      const stmt = this.db.db.prepare(`
        UPDATE ${this.tableName} 
        SET expire = ?, sess = ?
        WHERE sid = ?
      `);
      
      const result = stmt.run(expire.toISOString(), JSON.stringify(session), sid);
      
      if (callback) callback(null);
    } catch (error) {
      console.error('SQLiteSessionStore touch error:', error);
      if (callback) callback(error);
    }
  }

  /**
   * Get all session IDs (optional method)
   */
  all(callback) {
    try {
      const stmt = this.db.db.prepare(`
        SELECT sid, sess FROM ${this.tableName} 
        WHERE expire > datetime('now')
      `);
      const results = stmt.all();
      
      const sessions = {};
      results.forEach(row => {
        sessions[row.sid] = JSON.parse(row.sess);
      });
      
      callback(null, sessions);
    } catch (error) {
      console.error('SQLiteSessionStore all error:', error);
      callback(error);
    }
  }

  /**
   * Start periodic cleanup of expired sessions
   */
  startCleanup() {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredSessions();
    }, this.cleanupInterval);
    
    // Cleanup on process exit
    process.on('exit', () => {
      if (this.cleanupTimer) {
        clearInterval(this.cleanupTimer);
      }
    });
  }

  /**
   * Clean up expired sessions
   */
  cleanupExpiredSessions() {
    try {
      const stmt = this.db.db.prepare(`
        DELETE FROM ${this.tableName} 
        WHERE expire <= datetime('now')
      `);
      const result = stmt.run();
      
      if (result.changes > 0) {
        console.log(`🧹 Cleaned up ${result.changes} expired sessions`);
      }
    } catch (error) {
      console.error('SQLiteSessionStore cleanup error:', error);
    }
  }

  /**
   * Stop the cleanup timer
   */
  stopCleanup() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }
}

module.exports = SQLiteSessionStore;
