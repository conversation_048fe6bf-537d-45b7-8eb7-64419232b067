const db = require('../db');

// Function to check database schema
function checkDatabaseSchema() {
  console.log('Checking database schema...');

  // For JSON database, just check if users exist
  db.all(`SELECT * FROM users LIMIT 5`, [], (err, rows) => {
    if (err) {
      console.error('Error checking users:', err);
      return;
    }
    console.log('Sample users in database:', rows);
  });
}

module.exports = { checkDatabaseSchema };
