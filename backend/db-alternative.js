// Alternative database implementation using sql.js (pure JavaScript SQLite)
// This can be used if better-sqlite3 causes platform issues

const fs = require('fs');
const path = require('path');

// For now, we'll use a hybrid approach with JSON fallback
// In the future, this could be replaced with sql.js for pure JS SQLite

const dbPath = process.env.DB_PATH || path.join(__dirname, '../database.sqlite');
const jsonDbPath = path.join(__dirname, '../database.json');

let useJsonFallback = false;

// Try to use better-sqlite3, fall back to JSON if it fails
let Database, db;

try {
  Database = require('better-sqlite3');
  db = new Database(dbPath);
  console.log('✅ Using better-sqlite3 database');
} catch (error) {
  console.log('⚠️  better-sqlite3 failed, falling back to JSON database');
  console.log('Error:', error.message);
  useJsonFallback = true;
}

if (useJsonFallback) {
  // JSON Database Implementation
  function initJsonDatabase() {
    if (!fs.existsSync(jsonDbPath)) {
      const initialData = {
        users: [],
        charts: []
      };
      fs.writeFileSync(jsonDbPath, JSON.stringify(initialData, null, 2));
    }
  }

  function readJsonDatabase() {
    try {
      const data = fs.readFileSync(jsonDbPath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('Error reading JSON database:', error);
      initJsonDatabase();
      return { users: [], charts: [] };
    }
  }

  function writeJsonDatabase(data) {
    try {
      fs.writeFileSync(jsonDbPath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Error writing JSON database:', error);
      throw error;
    }
  }

  // JSON Database wrapper to mimic SQLite interface
  const dbWrapper = {
    run: function(sql, params, callback) {
      try {
        console.log('JSON DB operation:', sql, params);
        const data = readJsonDatabase();
        
        // Handle INSERT operations
        if (sql.includes('INSERT INTO users')) {
          const newUser = {
            id: data.users.length + 1,
            username: params[0],
            email: params[1],
            password: params[2] || null,
            google_id: params[3] || null,
            profile_picture: params[4] || null,
            auth_type: params[5] || 'local',
            reset_token: null,
            reset_token_expires: null,
            created_at: new Date().toISOString()
          };
          data.users.push(newUser);
          writeJsonDatabase(data);
          
          const context = { lastID: newUser.id, changes: 1 };
          if (callback) callback.call(context, null);
        }
        // Handle INSERT INTO charts
        else if (sql.includes('INSERT INTO charts')) {
          const newChart = {
            id: data.charts.length + 1,
            user_id: params[0],
            title: params[1],
            content: params[2],
            public: params[3] || 0,
            share_id: params[4] || null,
            folder: params[5] || '',
            notes: params[6] || '',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          data.charts.push(newChart);
          writeJsonDatabase(data);
          
          const context = { lastID: newChart.id, changes: 1 };
          if (callback) callback.call(context, null);
        }
        // Handle UPDATE operations
        else if (sql.includes('UPDATE users')) {
          // Implement user updates as needed
          const context = { changes: 1 };
          if (callback) callback.call(context, null);
        }
        else if (sql.includes('UPDATE charts')) {
          // Implement chart updates as needed
          const context = { changes: 1 };
          if (callback) callback.call(context, null);
        }
        else {
          if (callback) callback(null);
        }
      } catch (error) {
        console.error('JSON Database run error:', error);
        if (callback) callback(error);
      }
    },

    get: function(sql, params, callback) {
      try {
        const data = readJsonDatabase();
        
        if (sql.includes('SELECT * FROM users WHERE username = ?')) {
          const user = data.users.find(u => u.username === params[0]);
          if (callback) callback(null, user);
        } else if (sql.includes('SELECT * FROM users WHERE email = ?')) {
          const user = data.users.find(u => u.email === params[0]);
          if (callback) callback(null, user);
        } else if (sql.includes('SELECT * FROM users WHERE google_id = ?')) {
          const user = data.users.find(u => u.google_id === params[0]);
          if (callback) callback(null, user);
        } else if (sql.includes('SELECT * FROM users WHERE id = ?')) {
          const user = data.users.find(u => u.id === params[0]);
          if (callback) callback(null, user);
        } else if (sql.includes('SELECT * FROM charts WHERE id = ?')) {
          const chart = data.charts.find(c => c.id === params[0]);
          if (callback) callback(null, chart);
        } else {
          if (callback) callback(null, null);
        }
      } catch (error) {
        console.error('JSON Database get error:', error);
        if (callback) callback(error, null);
      }
    },

    all: function(sql, params, callback) {
      if (typeof params === 'function') {
        callback = params;
        params = [];
      }

      try {
        const data = readJsonDatabase();
        
        if (sql.includes('SELECT * FROM charts WHERE user_id = ?')) {
          const charts = data.charts.filter(c => c.user_id === params[0]);
          if (callback) callback(null, charts);
        } else if (sql.includes('SELECT * FROM charts')) {
          if (callback) callback(null, data.charts);
        } else if (sql.includes('SELECT * FROM users')) {
          if (callback) callback(null, data.users);
        } else {
          if (callback) callback(null, []);
        }
      } catch (error) {
        console.error('JSON Database all error:', error);
        if (callback) callback(error, []);
      }
    },

    serialize: function(callback) {
      if (callback) callback();
    }
  };

  // Initialize JSON database
  initJsonDatabase();
  
  module.exports = dbWrapper;

} else {
  // Use the existing better-sqlite3 implementation
  module.exports = require('./db.js');
}
