<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mermaid Download Functionality</title>
    <script src="public/js/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ccc;
            background-color: white;
            border-radius: 8px;
        }
        .download-buttons {
            margin: 10px 0;
            display: flex;
            gap: 10px;
        }
        .download-btn {
            padding: 8px 16px;
            background: #26a69a;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .download-btn:hover {
            background: #00d4aa;
        }
        .diagram-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 300px;
        }
    </style>
</head>
<body>
    <h1>🧪 Mermaid Download Functionality Test</h1>
    <p>This test verifies that Mermaid diagrams can be downloaded as PNG and SVG images.</p>

    <div class="test-container">
        <h2>Test Diagram</h2>
        <div class="download-buttons">
            <button class="download-btn" onclick="downloadChartAsImage('png', 'test-diagram-container')">📥 Download PNG</button>
            <button class="download-btn" onclick="downloadChartAsImage('svg', 'test-diagram-container')">📥 Download SVG</button>
        </div>

        <div id="test-diagram-container" class="diagram-container">
            <div id="diagram" class="mermaid">
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Process A]
    B -->|No| D[Process B]
    C --> E[End]
    D --> E
    E --> F[🎯 Success]
            </div>
        </div>
    </div>

    <script src="public/js/download-utils.js"></script>
    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: false,
            securityLevel: 'loose',
            theme: 'default',
            logLevel: 'fatal',
            fontFamily: 'monospace',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'linear'
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Initial render
            setTimeout(() => {
                try {
                    mermaid.init(undefined, document.getElementById('diagram'));
                    console.log('✅ Diagram rendered successfully');
                } catch (error) {
                    console.error('❌ Error rendering diagram:', error);
                }
            }, 100);
        });

        console.log('🚀 Download test loaded. Click the download buttons to test functionality.');

        // Add debug information
        console.log('Browser info:', {
            userAgent: navigator.userAgent,
            canvasSupport: !!document.createElement('canvas').getContext,
            blobSupport: typeof Blob !== 'undefined',
            urlSupport: typeof URL !== 'undefined'
        });
    </script>
</body>
</html>
