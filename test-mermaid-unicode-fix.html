<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mermaid Unicode Fix</title>
    <script src="public/js/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ccc;
            background-color: white;
            border-radius: 8px;
        }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        .info { color: blue; }
        pre {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-result.pass {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .test-result.fail {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .test-result.warn {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <h1>🧪 Mermaid Unicode Character Fix Test</h1>
    <p>This test verifies that the Unicode sanitization fix prevents the "UnknownDiagramError" while preserving valid Mermaid syntax.</p>

    <div class="test-container">
        <h2>Test 1: Original Problematic Content</h2>
        <p>Testing content that previously caused "UnknownDiagramError: No diagram type detected"</p>
        <div id="test1-content"></div>
        <div id="test1-result"></div>
    </div>

    <div class="test-container">
        <h2>Test 2: Unicode Characters in Labels</h2>
        <p>Testing various Unicode characters that should be safely replaced</p>
        <div id="test2-content"></div>
        <div id="test2-result"></div>
    </div>

    <div class="test-container">
        <h2>Test 3: Valid Mermaid Syntax Preservation</h2>
        <p>Testing that valid Mermaid syntax is preserved during sanitization</p>
        <div id="test3-content"></div>
        <div id="test3-result"></div>
    </div>

    <div class="test-container">
        <h2>Test 4: Edge Cases</h2>
        <p>Testing edge cases like empty content, whitespace, etc.</p>
        <div id="test4-content"></div>
        <div id="test4-result"></div>
    </div>

    <div class="test-container">
        <h2>Summary</h2>
        <div id="summary-result"></div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: false,
            securityLevel: 'loose',
            theme: 'default'
        });

        // Function to sanitize Mermaid content for problematic Unicode characters
        function sanitizeMermaidContent(content) {
            if (!content) return {
                content: content,
                hasReplacements: false,
                originalContent: content
            };

            // Map of problematic Unicode characters to safe alternatives
            const unicodeReplacements = {
                '➕': '+',
                '➖': '-',
                '✓': 'check',
                '✗': 'x',
                '⚠': 'warn',
                '⭐': 'star',
                '🔥': 'fire',
                '💡': 'idea',
                '📊': 'chart',
                '📈': 'up',
                '📉': 'down',
                '🎯': 'target',
                '⚡': 'fast',
                '🔒': 'lock',
                '🔓': 'unlock',
                '❌': 'x',
                '⭕': 'o',
                '🟢': 'green',
                '🔴': 'red',
                '🟡': 'yellow',
                '🔵': 'blue',
                '⚫': 'black',
                '⚪': 'white'
            };

            let sanitized = content;
            let hasReplacements = false;

            // Replace only the specific known problematic Unicode characters
            for (const [unicode, replacement] of Object.entries(unicodeReplacements)) {
                if (sanitized.includes(unicode)) {
                    sanitized = sanitized.split(unicode).join(replacement);
                    hasReplacements = true;
                }
            }

            // Only replace specific invisible/control characters that break parsing
            const invisibleCharReplacements = [
                [/[\u2000-\u200F]/g, ' '],  // Various spaces and invisible chars
                [/[\u2028-\u2029]/g, ' '],  // Line/paragraph separators
                [/[\uFEFF]/g, ''],          // Byte order mark (remove completely)
                [/[\u00A0]/g, ' ']          // Non-breaking space
            ];

            for (const [regex, replacement] of invisibleCharReplacements) {
                const matches = sanitized.match(regex);
                if (matches && matches.length > 0) {
                    sanitized = sanitized.replace(regex, replacement);
                    hasReplacements = true;
                }
            }

            return {
                content: sanitized,
                hasReplacements: hasReplacements,
                originalContent: content
            };
        }

        // Test cases
        const testCases = [
            {
                name: 'Problematic Unicode that caused original error',
                content: `graph TD
    A[Start] --> B{Decision}
    B -->|Yes| f1[➕]
    B -->|No| f2[➖]
    f1 --- f2`,
                expectValid: true
            },
            {
                name: 'Various Unicode characters',
                content: `flowchart LR
    A[🔥 Fire] --> B[💡 Idea]
    B --> C[📊 Chart]
    C --> D[⭐ Star]
    D --> E[✓ Valid]`,
                expectValid: true
            },
            {
                name: 'Valid Mermaid syntax without Unicode',
                content: `graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Process]
    B -->|No| D[End]
    C --> D`,
                expectValid: true
            },
            {
                name: 'Edge cases',
                content: '',
                expectValid: true
            }
        ];

        let totalTests = 0;
        let passedTests = 0;

        function runTest(testCase, index) {
            totalTests++;
            const contentDiv = document.getElementById(`test${index + 1}-content`);
            const resultDiv = document.getElementById(`test${index + 1}-result`);

            // Show original content
            contentDiv.innerHTML = `<h4>Original Content:</h4><pre>${testCase.content || '(empty)'}</pre>`;

            try {
                // Test sanitization
                const sanitizationResult = sanitizeMermaidContent(testCase.content);

                contentDiv.innerHTML += `<h4>Sanitized Content:</h4><pre>${sanitizationResult.content || '(empty)'}</pre>`;
                contentDiv.innerHTML += `<p><strong>Has Replacements:</strong> ${sanitizationResult.hasReplacements}</p>`;

                // Test Mermaid parsing
                if (sanitizationResult.content) {
                    try {
                        mermaid.parse(sanitizationResult.content);

                        // Test passed
                        passedTests++;
                        resultDiv.innerHTML = `<div class="test-result pass">
                            <strong>✅ PASS:</strong> ${testCase.name}<br>
                            Sanitized content parsed successfully by Mermaid
                        </div>`;

                        // Try to render if content exists
                        if (sanitizationResult.content.trim()) {
                            const renderDiv = document.createElement('div');
                            renderDiv.className = 'mermaid';
                            renderDiv.textContent = sanitizationResult.content;
                            resultDiv.appendChild(renderDiv);

                            setTimeout(() => {
                                try {
                                    mermaid.init(undefined, renderDiv);
                                } catch (renderError) {
                                    console.warn('Render warning for test', index + 1, ':', renderError.message);
                                }
                            }, 100 * (index + 1));
                        }

                    } catch (parseError) {
                        resultDiv.innerHTML = `<div class="test-result fail">
                            <strong>❌ FAIL:</strong> ${testCase.name}<br>
                            Parse error: ${parseError.message}
                        </div>`;
                    }
                } else {
                    // Empty content should be handled gracefully
                    passedTests++;
                    resultDiv.innerHTML = `<div class="test-result pass">
                        <strong>✅ PASS:</strong> ${testCase.name}<br>
                        Empty content handled correctly
                    </div>`;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result fail">
                    <strong>❌ FAIL:</strong> ${testCase.name}<br>
                    Unexpected error: ${error.message}
                </div>`;
            }
        }

        // Run all tests
        testCases.forEach((testCase, index) => {
            runTest(testCase, index);
        });

        // Show summary after a delay to let all tests complete
        setTimeout(() => {
            const summaryDiv = document.getElementById('summary-result');
            const successRate = Math.round((passedTests / totalTests) * 100);

            if (passedTests === totalTests) {
                summaryDiv.innerHTML = `<div class="test-result pass">
                    <h3>🎉 All Tests Passed!</h3>
                    <p><strong>${passedTests}/${totalTests}</strong> tests passed (${successRate}%)</p>
                    <p>The Unicode sanitization fix is working correctly and should prevent the "UnknownDiagramError".</p>
                </div>`;
            } else {
                summaryDiv.innerHTML = `<div class="test-result ${passedTests > 0 ? 'warn' : 'fail'}">
                    <h3>${passedTests > 0 ? '⚠️' : '❌'} Some Tests Failed</h3>
                    <p><strong>${passedTests}/${totalTests}</strong> tests passed (${successRate}%)</p>
                    <p>The fix may need further adjustment.</p>
                </div>`;
            }
        }, 2000);

        console.log('🚀 Unicode fix test loaded. Check the page for results.');
    </script>
</body>
</html>
