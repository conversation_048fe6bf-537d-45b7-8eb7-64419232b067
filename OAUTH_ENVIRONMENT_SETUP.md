# Google OAuth Environment-Based Configuration

## Overview

The Google OAuth configuration has been updated to automatically use the correct callback URL and CORS settings based on the environment (development vs production).

## Changes Made

### 1. Environment-Based Callback URL
- **Development**: `http://localhost:3000/api/auth/google/callback`
- **Production**: `https://mermantic.net/api/auth/google/callback`

The callback URL is now automatically determined based on the `NODE_ENV` environment variable.

### 2. Updated Files

#### `.env`
- Removed hardcoded `GOOGLE_CALLBACK_URL`
- Added comments explaining the automatic URL determination

#### `backend/config/passport.js`
- Added `getCallbackURL()` function that returns the appropriate callback URL based on environment
- Updated GoogleStrategy configuration to use dynamic callback URL

#### `backend/server.js`
- Added `getAllowedOrigins()` function for environment-based CORS configuration
- Updated CORS middleware to use appropriate origins for each environment
- Enhanced startup logging to show which callback URL and CORS origins are being used

#### `.env.example`
- Updated documentation to reflect the new environment-based approach
- Added instructions for configuring both development and production URLs in Google Cloud Console

## Google Cloud Console Configuration

You need to configure both environments in your Google Cloud Console:

### Authorized JavaScript Origins
- `http://localhost:3000` (for development)
- `https://mermantic.net` (for production)
- `https://www.mermantic.net` (for production with www)

### Authorized Redirect URIs
- `http://localhost:3000/api/auth/google/callback` (for development)
- `https://mermantic.net/api/auth/google/callback` (for production)

## Environment Variables

### Development
```bash
NODE_ENV=development  # or omit this variable
```

### Production
```bash
NODE_ENV=production
```

## Testing

### Development
1. Set `NODE_ENV=development` (or leave unset)
2. Start server: `npm start`
3. Check console output for callback URL: `http://localhost:3000/api/auth/google/callback`
4. Test Google OAuth at `http://localhost:3000/login.html`

### Production
1. Set `NODE_ENV=production`
2. Start server: `npm start`
3. Check console output for callback URL: `https://mermantic.net/api/auth/google/callback`
4. Test Google OAuth at `https://mermantic.net/login.html`

## Benefits

1. **No manual configuration changes** when switching between environments
2. **Automatic CORS configuration** for appropriate domains
3. **Clear logging** of which URLs are being used
4. **Single Google OAuth client** can handle both environments
5. **Reduced configuration errors** from hardcoded URLs

## Security Notes

- Production uses HTTPS-only callback URLs
- CORS is restricted to specific domains in production
- Session cookies are automatically configured for HTTPS in production
