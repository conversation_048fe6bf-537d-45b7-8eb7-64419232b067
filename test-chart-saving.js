// Test script to debug chart saving issues
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testChartSaving() {
  console.log('🧪 Testing Chart Saving Functionality...\n');

  try {
    // Test 1: Check session debug endpoint
    console.log('1. Testing session debug endpoint...');
    const sessionResponse = await fetch(`${BASE_URL}/api/charts/debug/session`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const sessionData = await sessionResponse.json();
    console.log('Session debug response:', sessionData);

    if (sessionData.hasUser) {
      console.log('✅ User found in session');
      
      // Test 2: Try to create a chart
      console.log('\n2. Testing chart creation...');
      const chartResponse = await fetch(`${BASE_URL}/api/charts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: 'Test Chart from Script',
          content: 'graph TD; A-->B; B-->C;',
          isPublic: false
        })
      });

      const chartData = await chartResponse.json();
      console.log('Chart creation response:', chartData);

      if (chartResponse.ok) {
        console.log('✅ Chart created successfully');
      } else {
        console.log('❌ Chart creation failed:', chartData.message);
      }
    } else {
      console.log('❌ No user in session');
      console.log('   This suggests the session is not being maintained after Google login');
    }

    // Test 3: Try to get all charts
    console.log('\n3. Testing chart retrieval...');
    const chartsResponse = await fetch(`${BASE_URL}/api/charts`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (chartsResponse.ok) {
      const chartsData = await chartsResponse.json();
      console.log('✅ Charts retrieved successfully:', chartsData.charts?.length || 0, 'charts');
    } else {
      const chartsError = await chartsResponse.json();
      console.log('❌ Chart retrieval failed:', chartsError.message);
    }

  } catch (error) {
    console.error('Test error:', error);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testChartSaving();
}

module.exports = { testChartSaving };
